import { <PERSON><PERSON>, Card, Radio, Tag } from "antd";
import { <PERSON><PERSON><PERSON>, <PERSON>, Eye, Palette, RefreshCw } from "lucide-react";
import React, { useState } from "react";
import EditorSnippet from "../../common/EditorSnippet";
import {
  cssPlaceholder,
  getPreviewHTML,
  htmlPlaceholder,
  jsPlaceholder,
  tabList,
} from "../content";

const TabList = ({ formData, setFormData }) => {
  const [placeholderInput, setPlaceholderInput] = useState("");
  const [previewMode, setPreviewMode] = useState("code");

  const tabContents = {
    code: {
      key: "html_content",
      type: "html",
      textareaId: "html-editor",
      placeholder: htmlPlaceholder,
      label: "HTML Code Editor",
      icon: <Code className="tw-w-4 tw-h-4 tw-text-orange-600 tw-mr-2" />,
    },
    css: {
      key: "css_content",
      type: "css",
      textareaId: "css-editor",
      placeholder: cssPlaceholder,
      label: "CSS",
      icon: <Palette className="tw-w-4 tw-h-4 tw-text-blue-600 tw-mr-2" />,
    },
    javascript: {
      key: "js_content",
      type: "js",
      textareaId: "js-editor",
      placeholder: jsPlaceholder,
      label: "JavaScript",
      icon: <Braces className="tw-w-4 tw-h-4 tw-text-yellow-600 tw-mr-2" />,
    },
    preview: {
      key: "preview",
      label: "Live Preview",
      icon: <Eye className="tw-w-4 tw-h-4 tw-text-green-600 tw-mr-2" />,
      content: (
        <div className="tw-p-0 tw-mb-4">
          {formData?.html_content ? (
            <iframe
              srcDoc={getPreviewHTML(formData)}
              className="tw-w-full tw-h-96 tw-border tw-border-gray-200 tw-rounded-lg"
              title="Component Preview"
            />
          ) : (
            <div className="tw-h-96 tw-flex tw-items-center tw-justify-center tw-bg-gray-50 tw-rounded-lg tw-border-2 tw-border-dashed tw-border-gray-300">
              <div className="tw-text-center">
                <Eye className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                <p className="tw-text-gray-500">
                  Add HTML content to see preview
                </p>
              </div>
            </div>
          )}
        </div>
      ),
    },
  };

  const addPlaceholder = () => {
    if (
      placeholderInput.trim() &&
      !formData.placeholders.includes(placeholderInput.trim())
    ) {
      setFormData({
        ...formData,
        placeholders: [...formData.placeholders, placeholderInput.trim()],
      });
      setPlaceholderInput("");
    }
  };

  const removePlaceholder = (index) => {
    setFormData({
      ...formData,
      placeholders: formData?.placeholders.filter((_, i) => i !== index),
    });
  };

  const autoDetectPlaceholders = () => {
    const content = formData?.html_content;
    if (!content) return;
    const regex = /\$\{([^}]+)\}/g;
    const matches = [];
    let match;

    while ((match = regex.exec(content)) !== null) {
      if (!matches.includes(match[1])) {
        matches.push(match[1]);
      }
    }

    setFormData({
      ...formData,
      placeholders: [...new Set([...formData.placeholders, ...matches])],
    });
  };

  return (
    <>
      {/* Placeholders Management */}
      {/* <Card
                title="Placeholders"
                className="tw-shadow-sm"
                extra={
                  <Button
                    type="link"
                    onClick={autoDetectPlaceholders}
                    icon={<RefreshCw className="tw-w-4 tw-h-4" />}
                    size="small"
                  >
                    Auto Detect
                  </Button>
                }
                headStyle={{
                  fontSize: "18px",
                  fontWeight: "600",
                  color: "#111827",
                }}
              >
                <div className="tw-flex tw-space-x-2 tw-mb-3">
                  <Input
                    value={placeholderInput}
                    onChange={(e) => setPlaceholderInput(e.target.value)}
                    onPressEnter={(e) => {
                      e.preventDefault();
                      addPlaceholder();
                    }}
                    placeholder="e.g., title, content, image_url"
                    size="large"
                    className="tw-flex-1"
                  />
                  <Button type="primary" onClick={addPlaceholder} size="large">
                    Add
                  </Button>
                </div>

                <div className="tw-flex tw-flex-wrap tw-gap-2">
                  {formData.placeholders.map((placeholder, index) => (
                    <span
                      key={index}
                      className="tw-inline-flex tw-items-center tw-px-3 tw-py-1 tw-bg-blue-100 tw-text-blue-800 tw-text-sm tw-rounded-full"
                    >
                      ${placeholder}
                      <button
                        type="button"
                        onClick={() => removePlaceholder(index)}
                        className="tw-ml-2 tw-text-blue-600 tw-hover:tw-text-blue-800"
                      >
                        <X className="tw-w-3 tw-h-3" />
                      </button>
                    </span>
                  ))}
                </div>

                {formData.placeholders.length === 0 && (
                  <p className="tw-text-sm tw-text-gray-500 tw-italic">
                    Add placeholders like ${"{title}"} or ${"{content}"} to make
                    your component dynamic
                  </p>
                )}
              </Card> */}
      {/* Right Panel - Code and Preview */}
      <div className="tw-space-y-4">
        {/* Preview/Code Toggle */}
        <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-4">
          <Radio.Group
            value={previewMode}
            onChange={(e) => setPreviewMode(e.target.value)}
            buttonStyle="solid"
            size="large"
            className="component-tab-list tw-w-full tw-p-[2px] tw-border tw-border-[#2563EB] tw-rounded-[10px]"
          >
            {tabList.map((tab) => (
              <Radio.Button
                key={tab.key}
                value={tab.key}
                className="tw-flex-1 tw-text-center !tw-rounded-[10px] before:!tw-w-0 tw-border-0 border-b-"
                style={{ width: "25%" }}
              >
                <div className="tw-flex tw-items-center tw-justify-center">
                  {tab.icon}
                  {tab?.tab}
                </div>
              </Radio.Button>
            ))}
          </Radio.Group>
        </div>
        <div className="tw-space-y-4">
          <Card className="tw-shadow-sm">
            <div className="tw-flex tw-items-center tw-mb-4">
              {tabContents[previewMode].icon}
              <span className="tw-text-sm tw-font-medium tw-text-gray-900">
                {tabContents[previewMode].label}
              </span>
            </div>
            {tabContents[previewMode].content ? (
              tabContents[previewMode].content
            ) : (
              <div className="tw-mb-4 tw-border tw-border-gray-300 tw-rounded-lg tw-overflow-hidden">
                <EditorSnippet
                  type={tabContents[previewMode].type}
                  defaultValue={formData?.[tabContents[previewMode].key]}
                  onValueChange={(code) =>
                    setFormData({
                      ...formData,
                      [tabContents[previewMode].key]: code,
                    })
                  }
                  value={formData?.[tabContents[previewMode].key]}
                  placeholder={tabContents[previewMode].placeholder}
                  textareaId={tabContents[previewMode].textareaId}
                />
              </div>
            )}
            <div className="tw-w-full">
              <div className="tw-w-full tw-flex tw-items-center tw-justify-between">
                <p className="tw-mb-[2px]">Placeholders:</p>
                <Button
                  type="link"
                  onClick={autoDetectPlaceholders}
                  icon={<RefreshCw className="tw-w-4 tw-h-4" />}
                  size="small"
                >
                  Auto Detect
                </Button>
              </div>

              {
                //   [
                //     "title",
                //     "content",
                //     "image_url",
                //     "alt_text",
                //     "button_text",
                //   ]
                formData?.placeholders?.map((placeholder, index) => (
                  <Tag color="blue" key={index}>
                    ${placeholder}
                  </Tag>
                ))
              }
            </div>
          </Card>
        </div>

        {/* {previewMode === "code" ? (
          <div className="tw-space-y-4">
            <Card className="tw-shadow-sm">
              <div className="tw-flex tw-items-center tw-mb-4">
                <Code className="tw-w-4 tw-h-4 tw-text-orange-600 tw-mr-2" />
                <span className="tw-text-sm tw-font-medium tw-text-gray-900">
                  HTML Code Editor
                </span>
              </div>
              <div className="tw-mb-4 tw-border tw-border-gray-300 tw-rounded-lg tw-overflow-hidden">
                <EditorSnippet
                  type="html"
                  defaultValue={formData?.html_content}
                  onValueChange={(code) =>
                    setFormData({
                      ...formData,
                      html_content: code,
                    })
                  }
                  placeholder={htmlPlaceholder}
                  textareaId="html-editor"
                />
              </div>
            </Card>
          </div>
        ) : previewMode === "css" ? (
          <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200">
            <div className="tw-flex tw-items-center tw-px-4 tw-py-3 tw-border-b tw-border-gray-200">
              <Palette className="tw-w-4 tw-h-4 tw-text-blue-600 tw-mr-2" />
              <span className="tw-text-sm tw-font-medium tw-text-gray-900">
                CSS
              </span>
            </div>
            <div className="tw-border-0">
              <EditorSnippet
                type="css"
                defaultValue={formData?.css_content}
                onValueChange={(code) =>
                  setFormData({
                    ...formData,
                    css_content: code,
                  })
                }
                placeholder={cssPlaceholder}
                textareaId="css-editor"
              />
            </div>
          </div>
        ) : previewMode === "javascript" ? (
          <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200">
            <div className="tw-flex tw-items-center tw-px-4 tw-py-3 tw-border-b tw-border-gray-200">
              <Braces className="tw-w-4 tw-h-4 tw-text-yellow-600 tw-mr-2" />
              <span className="tw-text-sm tw-font-medium tw-text-gray-900">
                JavaScript
              </span>
            </div>
            <div className="tw-border-0">
              <EditorSnippet
                type="js"
                defaultValue={formData?.js_content}
                onValueChange={(code) =>
                  setFormData({
                    ...formData,
                    js_content: code,
                  })
                }
                placeholder={jsPlaceholder}
                textareaId="js-editor"
              />
            </div>
          </div>
        ) : (
          <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200">
            <div className="tw-flex tw-items-center tw-px-4 tw-py-3 tw-border-b tw-border-gray-200">
              <Eye className="tw-w-4 tw-h-4 tw-text-green-600 tw-mr-2" />
              <span className="tw-text-sm tw-font-medium tw-text-gray-900">
                Live Preview
              </span>
            </div>
            <div className="tw-p-4">
              {formData.html_content ? (
                <iframe
                  srcDoc={getPreviewHTML()}
                  className="tw-w-full tw-h-96 tw-border tw-border-gray-200 tw-rounded-lg"
                  title="Component Preview"
                />
              ) : (
                <div className="tw-h-96 tw-flex tw-items-center tw-justify-center tw-bg-gray-50 tw-rounded-lg tw-border-2 tw-border-dashed tw-border-gray-300">
                  <div className="tw-text-center">
                    <Eye className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                    <p className="tw-text-gray-500">
                      Add HTML content to see preview
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )} */}
      </div>
    </>
  );
};

export default TabList;
