import React, { useState, useEffect, useRef } from "react";
import { Radio, Card, Row, Col, Input, Select, Button, Tag } from "antd";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/components/prism-markup";
import "prismjs/components/prism-css";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism-dark.css";

const { TextArea } = Input;

// Custom syntax highlighting functions
const highlightHTML = (code) => {
  return (
    Prism.highlight(code, Prism.languages.markup, "markup")
      // Add custom highlighting for placeholders ${...}
      .replace(
        /(\$\{)([^}]+)(\})/g,
        '<span style="color: #dcdcaa; font-weight: bold;">$1</span><span style="color: #9cdcfe; font-weight: bold;">$2</span><span style="color: #dcdcaa; font-weight: bold;">$3</span>'
      )
  );
};

const highlightCSS = (code) => {
  return Prism.highlight(code, Prism.languages.css, "css");
};

const highlightJS = (code) => {
  return Prism.highlight(code, Prism.languages.javascript, "javascript");
};

import Header from "../Layout/Header";
import { Save } from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import EditorSnippet from "../common/EditorSnippet";
import { cssPlaceholder, htmlPlaceholder, jsPlaceholder } from "./content";
import TabList from "./EditorComponent/TabList";

const ComponentEditor = ({ component, categories, onSave, onCancel }) => {
  const api = useHttp();
  const [formData, setFormData] = useState({
    name: "",
    category_id: "",
    html_content: "",
    css_content: "",
    js_content: "",
    placeholders: [],
  });

  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (component) {
      setFormData({
        name: component.name || "",
        category_id: component.category_id || "",
        html_content: component.html_content || "",
        css_content: component.css_content || "",
        js_content: component.js_content || "",
        placeholders: component.placeholders || [],
      });
    }
  }, [component]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    const apiConfig = component
      ? apiGenerator(CONSTANTS.API.components.update, { id: component.id })
      : CONSTANTS.API.components.create;

    api.sendRequest(
      apiConfig,
      (res) => {
        console.log("Component saved successfully:", res);
        setSaving(false);
        onSave();
      },
      formData,
      component
        ? "Component updated successfully!"
        : "Component created successfully!",
      (error) => {
        console.error("Error saving component:", error);
        setSaving(false);
      }
    );
  };

  return (
    <>
      <div className="tw-p-6">
        <form onSubmit={handleSubmit} className="tw-max-w-7xl tw-mx-auto">
          <div className="tw-grid tw-grid-cols-1 tw-lg:tw-grid-cols-2 tw-gap-4">
            {/* Left Panel - Component Details */}
            <div className="tw-space-y-2">
              <Card
                title="Component Details"
                className="tw-shadow-sm"
                headStyle={{
                  fontSize: "18px",
                  fontWeight: "600",
                  color: "#111827",
                }}
              >
                <Row gutter={[16, 16]}>
                  <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                    <div className="tw-mb-4">
                      <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                        Component Name
                      </label>
                      <Input
                        value={formData.name}
                        onChange={(e) =>
                          setFormData({ ...formData, name: e.target.value })
                        }
                        placeholder="e.g., Hero Section, Navigation Bar"
                        size="large"
                        required
                      />
                    </div>
                  </Col>
                  <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                    <div className="tw-mb-4">
                      <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                        Category
                      </label>
                      <Select
                        value={formData.category_id}
                        onChange={(value) =>
                          setFormData({
                            ...formData,
                            category_id: value,
                          })
                        }
                        placeholder="Select a category"
                        size="large"
                        className="tw-w-full"
                      >
                        {categories.map((category) => (
                          <Select.Option key={category.id} value={category.id}>
                            {category.name}
                          </Select.Option>
                        ))}
                      </Select>
                    </div>
                  </Col>
                </Row>
              </Card>
            </div>

            <TabList formData={formData} setFormData={setFormData} />
          </div>

          {/* Action Buttons */}
          <div className="tw-flex tw-justify-end tw-space-x-4 tw-mt-8 tw-pt-6 tw-border-t tw-border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="tw-px-6 tw-py-2 tw-border tw-border-gray-300 tw-text-gray-700 tw-rounded-lg tw-font-medium tw-hover:tw-bg-gray-50 tw-transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-6 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-disabled:tw-opacity-50"
            >
              {saving ? (
                <>
                  <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-white tw-mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="tw-w-4 tw-h-4 tw-mr-2" />
                  {component ? "Update Component" : "Create Component"}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </>
  );
};

export default ComponentEditor;
